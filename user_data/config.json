{"max_open_trades": 1, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1000, "timeframe": "5m", "cancel_open_orders_on_exit": false, "use_exit_signal": true, "exit_profit_only": false, "ignore_roi_if_entry_signal": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 50}, "pair_whitelist": ["WIF/USDT:USDT", "PENGU/USDT:USDT", "SPX/USDT:USDT", "CRV/USDT:USDT", "HBAR/USDT:USDT", "ETHFI/USDT:USDT", "CFX/USDT:USDT", "XLM/USDT:USDT", "ALGO/USDT:USDT", "BRETT/USDT:USDT", "ENA/USDT:USDT", "SEI/USDT:USDT", "FARTCOIN/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "XRP/USDT:USDT", "SUI/USDT:USDT", "DOT/USDT:USDT", "DOGE/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "LINK/USDT:USDT", "AVAX/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 14, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": true, "token": "", "chat_id": "", "keyboard": [["/daily", "/stats", "/balance", "/profit"], ["/status table", "/performance", "/whitelist"], ["/reload_config", "/count", "/logs"]], "notification_settings": {"status": "silent", "warning": "on", "startup": "silent", "entry": "silent", "exit": "off", "entry_cancel": "silent", "exit_cancel": "silent", "entry_fill": "silent", "exit_fill": "silent", "protection_trigger": "silent", "protection_trigger_global": "silent"}, "reload": true, "balance_dust_level": 0.01}, "api_server": {"enabled": false, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "info", "jwt_secret_key": "somethingrandom", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}