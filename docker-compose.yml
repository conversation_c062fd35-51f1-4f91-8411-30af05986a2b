---
services:
  freqtrade:
    # image: freqtradeorg/freqtrade:stable
    # image: freqtradeorg/freqtrade:develop
    # Use plotting image
    # image: freqtradeorg/freqtrade:develop_plot
    # # Enable GPU Image and GPU Resources (only relevant for freqAI)
    # # Make sure to uncomment the whole deploy section
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]
    # Build step - only needed when additional dependencies are needed
    build:
      context: .
      dockerfile: "./Dockerfile"
    restart: unless-stopped
    container_name: freqtrade
    volumes:
      - "./user_data:/freqtrade/user_data"
      - "./deploy/config.json:/freqtrade/config_production.json"
      - "./deploy/strategy_state.json:/freqtrade/user_data/strategy_state_production.json"
      - "./deploy/stop_entry_records.json:/freqtrade/user_data/stop_entry_records.json"
    # Expose api on port 8080 (localhost only)
    # Please read the https://www.freqtrade.io/en/stable/rest-api/ documentation
    # for more information.
    ports:
      - "8080:8080"
    # Default command used when running `docker compose up`
    command: >
      trade
      --logfile /freqtrade/user_data/logs/freqtrade.log
      --db-url sqlite:////freqtrade/user_data/tradesv3.sqlite
      --config /freqtrade/config_production.json
      --strategy KamaFama_Dynamic
    env_file:
      - .env
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
