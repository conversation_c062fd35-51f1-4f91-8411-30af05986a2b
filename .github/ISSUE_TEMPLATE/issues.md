---
name: Strategy bug report
about: Create a report to help us improve
title: ''
labels: "bug"
assignees: ''

---
<!-- 
Have you searched for similar issues before posting it?

Please only post bug-reports related to strategies in this repository.
For bugs with Freqtrade, please use the [freqtrade issue tracker](https://github.com/freqtrade/freqtrade/issues)

Please do not use bug reports to request new features.
-->

## Describe your environment

  * Operating system: ____
  * Python Version: _____ (`python -V`)
  * CCXT version: _____ (`pip freeze | grep ccxt`)
  * Freqtrade Version: ____ (`freqtrade -V` or `docker-compose run --rm freqtrade -V` for Freqtrade running in docker)
  * Strategy: _____
  
Note: All bug reports will be closed without further comment if the above template is deleted or not filled out.

## Describe the problem:

*Explain the problem you have encountered*

### Steps to reproduce:

  1. _____

  
### Observed Results:

  * What happened?
  * What did you expect to happen?

### Relevant code exceptions or logs

Note: Please copy/paste text of the messages, no screenshots of logs please.

  ```
  // paste your log here
  ```
